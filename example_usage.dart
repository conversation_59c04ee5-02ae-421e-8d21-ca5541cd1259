import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';
import 'lib/common/widget/pie_chart.dart';

// 示例数据模型
class ExampleData {
  final String name;
  final int value;
  final bool shouldShowLabel;

  ExampleData(this.name, this.value, this.shouldShowLabel);
}

class ExamplePieChartUsage extends StatelessWidget {
  const ExamplePieChartUsage({super.key});

  @override
  Widget build(BuildContext context) {
    // 示例数据
    List<ExampleData> data = [
      ExampleData('A类', 30, true),
      ExampleData('B类', 20, false), // 这个不显示标签
      ExampleData('C类', 25, true),
      ExampleData('D类', 15, true),
      ExampleData('E类', 10, false), // 这个不显示标签
    ];

    return Scaffold(
      appBar: AppBar(title: const Text('PieChart showLabel 示例')),
      body: Center(
        child: Pie<PERSON>hart(
          [
            // 方法1: 使用辅助方法创建 Series（推荐）
            PieChart.createSeriesWithShowLabel<ExampleData>(
              id: 'example',
              data: data,
              domainFn: (ExampleData datum, _) => datum.name,
              measureFn: (ExampleData datum, _) => datum.value,
              labelAccessorFn: (ExampleData datum, _) =>
                  '${datum.name}: ${datum.value}',
              colorFn: (ExampleData datum, int? index) {
                // 根据索引设置不同颜色
                final colors = [
                  charts.MaterialPalette.blue.shadeDefault,
                  charts.MaterialPalette.red.shadeDefault,
                  charts.MaterialPalette.green.shadeDefault,
                  charts.MaterialPalette.yellow.shadeDefault,
                  charts.MaterialPalette.purple.shadeDefault,
                ];
                return colors[index! % colors.length];
              },
              // 使用 showLabel 函数来控制每个数据项是否显示标签
              showLabel: (ExampleData datum, int? index) {
                return datum.shouldShowLabel;
              },
            ),
          ],
          showBehavior: true,
        ),
      ),
    );
  }
}

// 方法2: 手动创建带有 showLabel 逻辑的 Series 示例
class ManualExamplePieChartUsage extends StatelessWidget {
  const ManualExamplePieChartUsage({super.key});

  @override
  Widget build(BuildContext context) {
    List<ExampleData> data = [
      ExampleData('A类', 30, true),
      ExampleData('B类', 20, false),
      ExampleData('C类', 25, true),
      ExampleData('D类', 15, true),
      ExampleData('E类', 10, false),
    ];

    return Scaffold(
      appBar: AppBar(title: const Text('手动创建 Series 示例')),
      body: Center(
        child: PieChart(
          [
            charts.Series<ExampleData, String>(
              id: 'example',
              data: data,
              domainFn: (ExampleData datum, _) => datum.name,
              measureFn: (ExampleData datum, _) => datum.value,
              // 手动在 labelAccessorFn 中实现 showLabel 逻辑
              labelAccessorFn: (ExampleData datum, int? index) {
                // 如果不应该显示标签，返回空字符串
                if (!datum.shouldShowLabel) {
                  return '';
                }
                // 否则返回标签文本
                return '${datum.name}: ${datum.value}';
              },
              colorFn: (ExampleData datum, int? index) {
                final colors = [
                  charts.MaterialPalette.blue.shadeDefault,
                  charts.MaterialPalette.red.shadeDefault,
                  charts.MaterialPalette.green.shadeDefault,
                  charts.MaterialPalette.yellow.shadeDefault,
                  charts.MaterialPalette.purple.shadeDefault,
                ];
                return colors[index! % colors.length];
              },
            ),
          ],
          showBehavior: true,
        ),
      ),
    );
  }
}
