import 'package:flutter/material.dart';
import 'package:community_charts_flutter/community_charts_flutter.dart' as charts;
import 'lib/common/widget/pie_chart.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'PieChart showLabel 测试',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const TestPieChartPage(),
    );
  }
}

class TestPieChartPage extends StatelessWidget {
  const TestPieChartPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 测试数据
    final testData = [
      TestDataModel('项目A', 30, true),
      TestDataModel('项目B', 25, false), // 不显示标签
      TestDataModel('项目C', 20, true),
      TestDataModel('项目D', 15, false), // 不显示标签
      TestDataModel('项目E', 10, true),
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('PieChart showLabel 功能测试'),
        backgroundColor: Colors.blue,
      ),
      body: Container(
        color: Colors.black87,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                '饼图标签控制测试',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                '项目B和项目D不显示标签',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 40),
              SizedBox(
                width: 400,
                height: 400,
                child: PieChart(
                  [
                    PieChart.createSeriesWithShowLabel<TestDataModel>(
                      id: 'test_data',
                      data: testData,
                      domainFn: (TestDataModel datum, _) => datum.name,
                      measureFn: (TestDataModel datum, _) => datum.value,
                      labelAccessorFn: (TestDataModel datum, _) => 
                          '${datum.name}\n${datum.value}%',
                      colorFn: (TestDataModel datum, int? index) {
                        final colors = [
                          charts.MaterialPalette.blue.shadeDefault,
                          charts.MaterialPalette.red.shadeDefault,
                          charts.MaterialPalette.green.shadeDefault,
                          charts.MaterialPalette.yellow.shadeDefault,
                          charts.MaterialPalette.purple.shadeDefault,
                        ];
                        return colors[index! % colors.length];
                      },
                      showLabel: (TestDataModel datum, int? index) {
                        return datum.showLabel;
                      },
                    ),
                  ],
                  showBehavior: true,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class TestDataModel {
  final String name;
  final int value;
  final bool showLabel;

  TestDataModel(this.name, this.value, this.showLabel);
}
