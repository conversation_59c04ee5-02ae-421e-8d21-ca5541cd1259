import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;
import 'package:flutter/material.dart';

class <PERSON><PERSON><PERSON> extends StatelessWidget {
  final List<charts.Series<dynamic, String>> seriesList;
  final bool animate;
  final double? width;
  final double height;
  final bool showBehavior;
  final Function<bool>(dynamic data, int? index)? showLabel;

  const PieChart(
    this.seriesList, {
    super.key,
    this.animate = true,
    this.width,
    this.height = 270,
    this.showBehavior = false,
    this.showLabel,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? MediaQuery.of(context).size.width,
      height: height,
      child: charts.PieChart<String>(
        seriesList,
        animate: animate,
        defaultRenderer: charts.ArcRendererConfig<String>(
          arcRendererDecorators: [
            charts.ArcLabelDecorator<String>(
              labelPosition: charts.ArcLabelPosition.outside,
              // insideLabelStyleSpec: const charts.TextStyleSpec(
              //   fontSize: 12,
              //   color: charts.MaterialPalette.black,
              // ),
              outsideLabelStyleSpec: const charts.TextStyleSpec(
                fontSize: 12,
                color: charts.MaterialPalette.white,
              ),
            ),
          ],
        ),
        behaviors: showBehavior
            ? [
                charts.DatumLegend(
                  entryTextStyle: const charts.TextStyleSpec(
                    fontSize: 12,
                    color: charts.MaterialPalette.white,
                  ),
                ),
              ]
            : [],
      ),
    );
  }

  /// 创建一个辅助方法来帮助用户创建带有 showLabel 逻辑的 Series
  static charts.Series<T, String> createSeriesWithShowLabel<T>({
    required String id,
    required List<T> data,
    required String Function(T, int?) domainFn,
    required num? Function(T, int?) measureFn,
    charts.Color Function(T, int?)? colorFn,
    String? Function(T, int?)? labelAccessorFn,
    required bool Function(T, int?) showLabel,
  }) {
    return charts.Series<T, String>(
      id: id,
      data: data,
      domainFn: domainFn,
      measureFn: measureFn,
      colorFn: colorFn,
      labelAccessorFn: (datum, index) {
        // 调用 showLabel 函数来决定是否显示标签
        bool shouldShow = showLabel(datum, index);
        if (!shouldShow) {
          return ''; // 返回空字符串表示不显示标签
        }

        // 如果应该显示标签，使用提供的 labelAccessorFn 或默认逻辑
        if (labelAccessorFn != null) {
          return labelAccessorFn(datum, index) ?? '';
        } else {
          // 如果没有提供 labelAccessorFn，返回 domain 值
          return domainFn(datum, index);
        }
      },
    );
  }
}
