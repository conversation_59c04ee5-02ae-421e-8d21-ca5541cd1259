# PieChart showLabel 功能说明

## 概述

现在 `PieChart` 组件支持通过 `showLabel` 函数来控制每个数据项是否显示标签。这个功能允许你根据数据内容和索引来动态决定哪些扇形区域显示标签。

## 函数签名

```dart
final Function<bool>(dynamic data, int? index)? showLabel;
```

- `data`: seriesList 中的每个数据项
- `index`: 数据项在列表中的索引
- 返回值: `true` 显示标签，`false` 不显示标签

## 使用方法

### 方法1: 使用辅助方法（推荐）

使用 `PieChart.createSeriesWithShowLabel` 静态方法来创建带有 showLabel 逻辑的 Series：

```dart
PieChart(
  [
    PieChart.createSeriesWithShowLabel<YourDataType>(
      id: 'your_series_id',
      data: yourDataList,
      domainFn: (datum, _) => datum.name,
      measureFn: (datum, _) => datum.value,
      labelAccessorFn: (datum, _) => '${datum.name}: ${datum.value}',
      colorFn: (datum, index) => yourColorFunction(datum, index),
      showLabel: (datum, index) {
        // 在这里实现你的标签显示逻辑
        return datum.shouldShowLabel; // 或者其他逻辑
      },
    ),
  ],
  showBehavior: true,
)
```

### 方法2: 手动实现

在 `charts.Series` 的 `labelAccessorFn` 中手动实现 showLabel 逻辑：

```dart
PieChart(
  [
    charts.Series<YourDataType, String>(
      id: 'your_series_id',
      data: yourDataList,
      domainFn: (datum, _) => datum.name,
      measureFn: (datum, _) => datum.value,
      labelAccessorFn: (datum, index) {
        // 如果不应该显示标签，返回空字符串
        if (!datum.shouldShowLabel) {
          return '';
        }
        // 否则返回标签文本
        return '${datum.name}: ${datum.value}';
      },
      colorFn: (datum, index) => yourColorFunction(datum, index),
    ),
  ],
  showBehavior: true,
)
```

## 示例

### 基本示例

```dart
class ExampleData {
  final String name;
  final int value;
  final bool shouldShowLabel;

  ExampleData(this.name, this.value, this.shouldShowLabel);
}

// 数据
List<ExampleData> data = [
  ExampleData('A类', 30, true),
  ExampleData('B类', 20, false), // 不显示标签
  ExampleData('C类', 25, true),
  ExampleData('D类', 15, true),
  ExampleData('E类', 10, false), // 不显示标签
];

// 使用
PieChart(
  [
    PieChart.createSeriesWithShowLabel<ExampleData>(
      id: 'example',
      data: data,
      domainFn: (datum, _) => datum.name,
      measureFn: (datum, _) => datum.value,
      labelAccessorFn: (datum, _) => '${datum.name}: ${datum.value}%',
      showLabel: (datum, index) => datum.shouldShowLabel,
    ),
  ],
  showBehavior: true,
)
```

### 基于索引的示例

```dart
// 只显示前3个数据项的标签
showLabel: (datum, index) {
  return index != null && index < 3;
}
```

### 基于数值的示例

```dart
// 只显示数值大于20的数据项的标签
showLabel: (datum, index) {
  if (datum is ExampleData) {
    return datum.value > 20;
  }
  return true;
}
```

## 注意事项

1. 当 `showLabel` 函数返回 `false` 时，对应的数据项将不显示标签，但扇形区域仍然会显示
2. 如果没有提供 `showLabel` 函数，所有数据项都会显示标签（默认行为）
3. 推荐使用方法1（辅助方法），因为它提供了更好的类型安全性和代码可读性
4. 标签的样式仍然由 `ArcLabelDecorator` 的 `outsideLabelStyleSpec` 控制

## 兼容性

这个功能与现有的 PieChart 组件完全兼容，不会影响现有代码的使用。
